# Debug buyer creation
$BASE_URL = "http://localhost:4000"

Write-Host "=== Debug Buyer Creation ===" -ForegroundColor Green
Write-Host

# Step 1: Login with existing user
Write-Host "Step 1: Login with existing user" -ForegroundColor Yellow
$loginBody = @{
    email = "<EMAIL>"
    password = "Password123!"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$BASE_URL/users/log_in" -Method POST -ContentType "application/json" -Body $loginBody
    Write-Host "Login Success!" -ForegroundColor Green
    $authToken = $response.data.token
    $userId = $response.data.id
    Write-Host "User ID: $userId" -ForegroundColor Cyan
    Write-Host "Auth Token: $authToken" -ForegroundColor Cyan
} catch {
    Write-Host "Login Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
    exit 1
}
Write-Host "---"

# Step 2: Try to create a buyer with minimal required fields
Write-Host "Step 2: Create buyer with minimal fields" -ForegroundColor Yellow
$buyerBody = @{
    first_name = "John"
    last_name = "Doe"
    buyer_locations_of_interest = @("601")
    buyer_expiration_date = "2025-12-31T23:59:59Z"
    buyer_need = @{
        purchase_type = "purchase"
        property_type = "single_family_house"
        financial_status = "pre_approved"
        budget_upto = "500000"
        min_bedrooms = 3
        min_bathrooms = 2
        min_area = 1500
    }
} | ConvertTo-Json

Write-Host "Request Body: $buyerBody" -ForegroundColor Cyan

try {
    $headers = @{ "Authorization" = $authToken }
    $response = Invoke-RestMethod -Uri "$BASE_URL/buyer" -Method POST -ContentType "application/json" -Body $buyerBody -Headers $headers
    Write-Host "Buyer Creation Success!" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Green
} catch {
    Write-Host "Buyer Creation Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
        Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Cyan
    }
}
Write-Host "---"

Write-Host "=== Debug completed ===" -ForegroundColor Green
