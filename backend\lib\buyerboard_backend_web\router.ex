defmodule BuyerboardBackendWeb.Router do
  use BuyerboardBackendWeb, :router

  import BuyerboardBackendWeb.UserAuth

  if Mix.env() == :dev do
    forward("/sent_emails", Bamboo.SentEmailViewerPlug)
  end

  pipeline :browser do
    plug :accepts, ["html", "json"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {BuyerboardBackendWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_user
    # Add this plug to capture errors for Sentry
    plug Sentry.PlugContext
  end

  pipeline :api do
    plug :accepts, ["json"]
    # Add this plug to capture errors for Sentry
    plug Sentry.PlugContext
  end

  pipeline :check_user_status do
    plug BuyerboardBackendWeb.Plugs.CheckAccountStatus
  end

  scope "/", BuyerboardBackendWeb do
    pipe_through :browser

    get "/", PageController, :home
  end

  # Other scopes may use custom stacks.
  # scope "/api", BuyerboardBackendWeb do
  #   pipe_through :api
  # end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:buyerboard_backend, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through :browser

      live_dashboard "/dashboard", metrics: BuyerboardBackendWeb.Telemetry
      forward "/mailbox", Plug.Swoosh.MailboxPreview
    end
  end

  ## Authentication routes

  scope "/", BuyerboardBackendWeb do
    pipe_through [:api, :redirect_if_user_is_authenticated]
    post "/users/reset_password/verify_token", UserResetPasswordController, :verify_otp_and_email
    put "/users/reset_password", UserResetPasswordController, :update
    post "/delete-account", UserController, :send_delete_link
    get "/delete/:hash", UserController, :delete_account
  end

  scope "/", BuyerboardBackendWeb do
    pipe_through [:api, :redirect_if_user_is_authenticated, :check_user_status]
    post "/users/register", UserController, :create
    post "/users/log_in", UserSessionController, :create
    post "/users/reset_password", UserResetPasswordController, :create
    post "/social-sign-up", UserController, :social_sign_up
    post "/apple-sign-up", UserController, :apple_sign_up

  end


  scope "/", BuyerboardBackendWeb do
    pipe_through [:browser, :require_authenticated_user]

    get "/users/settings", UserSettingsController, :edit
    put "/users/settings", UserSettingsController, :update
    get "/users/settings/confirm_email/:token", UserSettingsController, :confirm_email
  end

  scope "/", BuyerboardBackendWeb do
    pipe_through [:api, :fetch_logged_in_user]

    delete "/users/log_out", UserSessionController, :log_out
    # User's profile routes
    put "/users/profile", ProfileController, :update_profile
    get "/user/:id", UserController, :show
    delete "/user/delete", BuyerController, :delete_user
    # Buyer's routes
    get "/buyers", BuyerController, :index
    get "/buyer/:id", BuyerController, :show
    get "/user_buyers", BuyerController, :user_buyers
    get "/other_buyers", BuyerController, :other_buyers
    get "/favourite_buyers", BuyerController, :favourite_buyers
    post "/favourite_buyer/:buyer_id", BuyerController, :favourite_buyer
    post "/buyer", BuyerController, :create
    post "/filtered_buyers", BuyerController, :filtered_buyers
    post "/search_buyers/:zip_code", BuyerController, :search_buyers
    put "/buyer/:id", BuyerController, :update
    # Note for buyer route
    post "/buyer_note/:buyer_id", NoteController, :create
    # Uploading image route
    post "/upload_image", ImageController, :create
    post "/upload_attachment", ImageController, :upload_attachment
    post "/delete_attachments", ImageController, :delete_uploaded_attachments
    # Location's routes
    get "/locations", LocationController, :index
    get "/location/:zip_code", LocationController, :show
    get "/states", LocationController, :state_index
    post "/buyer_locations", LocationController, :buyer_locations
    # Auth provider routes
    post "/auth_provider", AuthProviderController, :create
  end

  scope "/api/swagger" do
    forward "/", PhoenixSwagger.Plug.SwaggerUI,
      otp_app: :buyerboard_backend,
      swagger_file: "swagger.json"
  end

  def swagger_info do
    %{
      schemes: ["https", "http", "ws", "wss"],
      host: "bb.vdev.tech/api",
      info: %{
        version: "1.0",
        title: "MyAPI",
        description: "API Documentation for MyAPI v1",
        termsOfService: "Open for public",
        contact: %{
          name: "Vladimir Gorej",
          email: "<EMAIL>"
        }
      },
      securityDefinitions: %{
        Bearer: %{
          type: "apiKey",
          name: "Authorization",
          description: "Api Token must be provided via `Authorization: Bearer ` header",
          in: "header"
        }
      },
      consumes: ["application/json"],
      produces: ["application/json"],
      tags: [
        %{name: "Users", description: "User resources"}
      ]
    }
  end
end
