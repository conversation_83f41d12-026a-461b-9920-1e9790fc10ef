import Config

# Configure your database
if System.get_env("DATABASE_URL") do
  config :buyerboard_backend, BuyerboardBackend.Repo,
    url: System.get_env("DATABASE_URL"),
    stacktrace: true,
    show_sensitive_data_on_connection_error: true,
    pool_size: 10
else
  config :buyerboard_backend, BuyerboardBackend.Repo,
    username: "postgres",
    password: "postgres",
    hostname: "localhost",
    database: "buyerboard_backend_dev",
    stacktrace: true,
    show_sensitive_data_on_connection_error: true,
    pool_size: 10
end

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we can use it
# to bundle .js and .css sources.
config :buyerboard_backend, BuyerboardBackendWeb.Endpoint,
  # Binding to loopback ipv4 address prevents access from other machines.
  # Change to `ip: {0, 0, 0, 0}` to allow access from other machines.
  http: [ip: {127, 0, 0, 1}, port: 4000],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "1yOedazqXkAwmxApTIyr12yiMaj4YIMLTo73pLlwXsorxfYXfafTeMorG3GjNpFx",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:buyerboard_backend, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:buyerboard_backend, ~w(--watch)]}
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Watch static and templates for browser reloading.
config :buyerboard_backend, BuyerboardBackendWeb.Endpoint,
  live_reload: [
    patterns: [
      ~r"priv/static/(?!uploads/).*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/buyerboard_backend_web/(controllers|live|components)/.*(ex|heex)$"
    ]
  ]

config :buyerboard_backend, BuyerboardBackend.Mailer,
  adapter: Bamboo.SendGridAdapter,
  api_key: "*********************************************************************",
  generic_transactional_template: "d-5879aaa8ecaf4eceabc76a4a017069c0",
  password_reset_template: "d-2b58618abc304abc85fe7b40d5e0c4f4",
  email_template: "d-c254e486bf4c430781956dfb2ecf511c",
  no_reply_email: "<EMAIL>",
  reply_to_domain: "dev-inbox.buyerboard.com"

config :sentry,
  dsn: "https://<EMAIL>/4",
  environment_name: Mix.env(),
  enable_source_code_context: true,
  root_source_code_paths: [File.cwd!()],
  send_event: false

config :pusher,
  app_id: "1:320516824618:android:fbf6216149cec218c76d53",
  app_key: "c88084ab-46fb-426e-8b13-f76e4f5a6189",
  secret: "7ACFFDA1284FB3BA76DB333B3EA2837E9E5C73D0F6E9602D863B735CC9DA3490"

# Enable dev routes for dashboard and mailbox
config :buyerboard_backend, dev_routes: true

# Do not include metadata nor timestamps in development logs
config :logger, :console, format: "[$level] $message\n"

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime

config :phoenix_live_view,
  # Include HEEx debug annotations as HTML comments in rendered markup
  debug_heex_annotations: true,
  # Enable helpful, but potentially expensive runtime checks
  enable_expensive_runtime_checks: true

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false
