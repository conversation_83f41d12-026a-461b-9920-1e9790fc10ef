version: '3.8'

services:
  db:
    image: postgis/postgis:15-3.3
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: buyerboard_backend_dev
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  web:
    build: .
    ports:
      - "4000:4000"
    depends_on:
      - db
    environment:
      DATABASE_URL: "ecto://postgres:postgres@db/buyerboard_backend_dev"
      SECRET_KEY_BASE: "your-secret-key-base-here-make-it-long-and-random"
      MIX_ENV: dev
    volumes:
      - .:/app
      - /app/deps
      - /app/_build
    command: >
      sh -c "
        mix deps.get &&
        mix ecto.create &&
        mix ecto.migrate &&
        mix phx.server
      "

volumes:
  postgres_data:
