# Test with curl for better error messages
$BASE_URL = "http://localhost:4000"

Write-Host "=== Test with curl ===" -ForegroundColor Green
Write-Host

# Step 1: Login
Write-Host "Step 1: Login" -ForegroundColor Yellow
$loginJson = '{"email":"<EMAIL>","password":"Password123!"}'
$loginResponse = curl -s -X POST "$BASE_URL/users/log_in" -H "Content-Type: application/json" -d $loginJson
Write-Host "Login Response: $loginResponse" -ForegroundColor Cyan

# Extract token (simple parsing)
$loginData = $loginResponse | ConvertFrom-Json
$authToken = $loginData.data.token
Write-Host "Auth Token: $authToken" -ForegroundColor Green
Write-Host "---"

# Step 2: Create buyer
Write-Host "Step 2: Create buyer with curl" -ForegroundColor Yellow
$buyerJson = '{"first_name":"<PERSON>","last_name":"<PERSON><PERSON>","buyer_locations_of_interest":["601"],"buyer_expiration_date":"2025-12-31"}'
Write-Host "Request: $buyerJson" -ForegroundColor Cyan

$buyerResponse = curl -s -X POST "$BASE_URL/buyer" -H "Content-Type: application/json" -H "Authorization: $authToken" -d $buyerJson
Write-Host "Buyer Response: $buyerResponse" -ForegroundColor Cyan
Write-Host "---"

Write-Host "=== Test completed ===" -ForegroundColor Green
