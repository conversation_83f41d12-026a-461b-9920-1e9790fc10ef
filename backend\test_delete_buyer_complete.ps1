# Complete test script for delete buyer functionality
$BASE_URL = "http://localhost:4000"

Write-Host "=== Complete Delete Buyer Test ===" -ForegroundColor Green
Write-Host

# Step 1: Register a test user
Write-Host "Step 1: Register a test user" -ForegroundColor Yellow
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$testEmail = "testuser$<EMAIL>"
$testPassword = "Password123!"

$registerBody = @{
    email = $testEmail
    password = $testPassword
    password_confirmation = $testPassword
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$BASE_URL/users/register" -Method POST -ContentType "application/json" -Body $registerBody
    Write-Host "Registration Success!" -ForegroundColor Green
    $authToken = $response.data.token
    $userId = $response.data.id
    Write-Host "User ID: $userId" -ForegroundColor Cyan
    Write-Host "Auth Token: $authToken" -ForegroundColor Cyan
} catch {
    Write-Host "Registration Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
    exit 1
}
Write-Host "---"

# Step 2: Create a buyer
Write-Host "Step 2: Create a buyer" -ForegroundColor Yellow
$buyerBody = @{
    first_name = "John"
    last_name = "Doe"
    email = "<EMAIL>"
    primary_phone_number = "+****************"
    buyer_locations_of_interest = @("601", "602")
    additional_requests = "Looking for a 3-bedroom house"
    buyer_expiration_date = "2025-12-31"
} | ConvertTo-Json

try {
    $headers = @{ "Authorization" = $authToken }
    $response = Invoke-RestMethod -Uri "$BASE_URL/buyer" -Method POST -ContentType "application/json" -Body $buyerBody -Headers $headers
    Write-Host "Buyer Creation Success!" -ForegroundColor Green
    $buyerId = $response.data.id
    Write-Host "Buyer ID: $buyerId" -ForegroundColor Cyan
    Write-Host "Buyer Name: $($response.data.first_name) $($response.data.last_name)" -ForegroundColor Cyan
} catch {
    Write-Host "Buyer Creation Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
    exit 1
}
Write-Host "---"

# Step 3: Verify buyer exists
Write-Host "Step 3: Verify buyer exists" -ForegroundColor Yellow
try {
    $headers = @{ "Authorization" = $authToken }
    $response = Invoke-RestMethod -Uri "$BASE_URL/buyer/$buyerId" -Method GET -ContentType "application/json" -Headers $headers
    Write-Host "Buyer Retrieved Successfully!" -ForegroundColor Green
    Write-Host "Buyer: $($response.data.first_name) $($response.data.last_name)" -ForegroundColor Cyan
} catch {
    Write-Host "Buyer Retrieval Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}
Write-Host "---"

# Step 4: Test delete buyer without authentication (should fail)
Write-Host "Step 4: Test delete buyer without authentication (should fail)" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BASE_URL/buyer/$buyerId" -Method DELETE -ContentType "application/json"
    Write-Host "Unexpected Success: $($response | ConvertTo-Json)" -ForegroundColor Red
} catch {
    Write-Host "Expected Error (no auth): $($_.Exception.Message)" -ForegroundColor Green
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Green
        Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Cyan
    }
}
Write-Host "---"

# Step 5: Test delete buyer with authentication (should succeed)
Write-Host "Step 5: Test delete buyer with authentication (should succeed)" -ForegroundColor Yellow
try {
    $headers = @{ "Authorization" = $authToken }
    $response = Invoke-RestMethod -Uri "$BASE_URL/buyer/$buyerId" -Method DELETE -ContentType "application/json" -Headers $headers
    Write-Host "Delete Success: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "Delete Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
        Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Cyan
    }
}
Write-Host "---"

# Step 6: Verify buyer is deleted
Write-Host "Step 6: Verify buyer is deleted (should return 404)" -ForegroundColor Yellow
try {
    $headers = @{ "Authorization" = $authToken }
    $response = Invoke-RestMethod -Uri "$BASE_URL/buyer/$buyerId" -Method GET -ContentType "application/json" -Headers $headers
    Write-Host "Unexpected Success (buyer still exists): $($response | ConvertTo-Json)" -ForegroundColor Red
} catch {
    Write-Host "Expected Error (buyer deleted): $($_.Exception.Message)" -ForegroundColor Green
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Green
        Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Cyan
    }
}
Write-Host "---"

Write-Host "=== Test completed ===" -ForegroundColor Green
