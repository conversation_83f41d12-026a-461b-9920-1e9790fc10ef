# Use the official Elixir image
FROM elixir:1.17-alpine

# Install build dependencies
RUN apk add --no-cache \
    build-base \
    git \
    nodejs \
    npm \
    postgresql-client

# Set environment variables
ENV MIX_ENV=dev
ENV PORT=4000

# Create app directory
WORKDIR /app

# Install hex and rebar
RUN mix local.hex --force && \
    mix local.rebar --force

# Copy mix files
COPY mix.exs ./
COPY mix.loc[k] ./

# Install dependencies
RUN mix deps.get

# Copy source code
COPY . .

# Compile the project
RUN mix compile

# Expose port
EXPOSE 4000

# Default command
CMD ["mix", "phx.server"]
