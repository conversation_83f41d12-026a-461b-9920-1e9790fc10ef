# Test script to check available locations
$BASE_URL = "http://localhost:4000"

Write-Host "=== Testing Locations ===" -ForegroundColor Green
Write-Host

# Test 1: Get all locations
Write-Host "Test 1: Get all locations" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BASE_URL/locations" -Method GET -ContentType "application/json"
    Write-Host "Locations Success: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Green
} catch {
    Write-Host "Locations Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
        Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Cyan
    }
}
Write-Host "---"

Write-Host "=== Test completed ===" -ForegroundColor Green
