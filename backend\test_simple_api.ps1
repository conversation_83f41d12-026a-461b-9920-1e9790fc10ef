# Simple API test script
$BASE_URL = "http://localhost:4000"

Write-Host "=== Simple API Test ===" -ForegroundColor Green
Write-Host

# Test 1: Try to register a user
Write-Host "Test 1: Register a user" -ForegroundColor Yellow
$registerBody = @{
    email = "<EMAIL>"
    password = "Password123!"
    password_confirmation = "Password123!"
} | ConvertTo-Json

Write-Host "Request Body: $registerBody" -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "$BASE_URL/users/register" -Method POST -ContentType "application/json" -Body $registerBody
    Write-Host "Registration Success: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Green
} catch {
    Write-Host "Registration Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
        Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Cyan
    }
}
Write-Host "---"

# Test 2: Try to login
Write-Host "Test 2: Login" -ForegroundColor Yellow
$loginBody = @{
    email = "<EMAIL>"
    password = "Password123!"
} | ConvertTo-Json

Write-Host "Request Body: $loginBody" -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "$BASE_URL/users/log_in" -Method POST -ContentType "application/json" -Body $loginBody
    Write-Host "Login Success: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Green
} catch {
    Write-Host "Login Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
        Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Cyan
    }
}
Write-Host "---"

Write-Host "=== Test completed ===" -ForegroundColor Green
