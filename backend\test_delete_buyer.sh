#!/bin/bash

# Test script for delete buyer functionality
BASE_URL="http://localhost:4000"

echo "=== Testing Delete Buyer Functionality ==="
echo

# Test 1: Try to delete without authentication
echo "Test 1: Delete buyer without authentication (should fail)"
curl -X DELETE "$BASE_URL/buyer/1" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo
echo "---"

# Test 2: Try to delete non-existent buyer
echo "Test 2: Delete non-existent buyer (should return 404)"
curl -X DELETE "$BASE_URL/buyer/99999" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer fake-token" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo
echo "---"

# Test 3: Check if there are any buyers first
echo "Test 3: Get all buyers to see what's available"
curl -X GET "$BASE_URL/buyers" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo
echo "---"

echo "=== Test completed ==="
