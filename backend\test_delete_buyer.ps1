# Test script for delete buyer functionality
$BASE_URL = "http://localhost:4000"

Write-Host "=== Testing Delete Buyer Functionality ===" -ForegroundColor Green
Write-Host

# Test 1: Try to delete without authentication
Write-Host "Test 1: Delete buyer without authentication (should fail)" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/buyer/1" -Method DELETE -ContentType "application/json" -ErrorAction SilentlyContinue
    Write-Host "Response: $($response.Content)"
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
        Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Cyan
    }
}
Write-Host "---"

# Test 2: Try to create a test user and login to get a token
Write-Host "Test 2: Create test user and login to get authentication token" -ForegroundColor Yellow
$testEmail = "<EMAIL>"
$testPassword = "password123"

# Try to register a test user
try {
    $registerBody = @{
        email = $testEmail
        password = $testPassword
        password_confirmation = $testPassword
    } | ConvertTo-Json

    $response = Invoke-WebRequest -Uri "$BASE_URL/users/register" -Method POST -ContentType "application/json" -Body $registerBody -ErrorAction SilentlyContinue
    Write-Host "Registration Response: $($response.Content)"
    Write-Host "Registration Status: $($response.StatusCode)" -ForegroundColor Cyan
} catch {
    Write-Host "Registration Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Registration Response Body: $responseBody" -ForegroundColor Red
    }
}

# Try to login to get token
try {
    $loginBody = @{
        email = $testEmail
        password = $testPassword
    } | ConvertTo-Json

    $response = Invoke-WebRequest -Uri "$BASE_URL/users/log_in" -Method POST -ContentType "application/json" -Body $loginBody -ErrorAction SilentlyContinue
    $loginData = $response.Content | ConvertFrom-Json
    Write-Host "Login Response: $($response.Content)"
    Write-Host "Login Status: $($response.StatusCode)" -ForegroundColor Cyan

    if ($loginData.user.token) {
        $authToken = "Bearer " + $loginData.user.token
        Write-Host "Got auth token: $authToken" -ForegroundColor Green
    }
} catch {
    Write-Host "Login Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Login Response Body: $responseBody" -ForegroundColor Red
    }
}
Write-Host "---"

# Test 3: Get all buyers with authentication
Write-Host "Test 3: Get all buyers with authentication" -ForegroundColor Yellow
if ($authToken) {
    try {
        $headers = @{ "Authorization" = $authToken }
        $response = Invoke-WebRequest -Uri "$BASE_URL/buyers" -Method GET -ContentType "application/json" -Headers $headers -ErrorAction SilentlyContinue
        Write-Host "Response: $($response.Content)"
        Write-Host "Status: $($response.StatusCode)" -ForegroundColor Cyan
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody" -ForegroundColor Red
            Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Cyan
        }
    }
} else {
    Write-Host "No auth token available, skipping authenticated test" -ForegroundColor Red
}
Write-Host "---"

Write-Host "=== Test completed ===" -ForegroundColor Green
